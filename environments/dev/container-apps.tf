module "container_environment" {
  source = "../../modules/container-app-environment"

  name                       = "${var.environment}-app-capp"
  resource_group_name        = azurerm_resource_group.app.name
  location                   = azurerm_resource_group.app.location
  infrastructure_subnet_id   = module.networking_app.subnet_ids["${var.environment}-containerapps-snet"]
  log_analytics_workspace_id = azurerm_log_analytics_workspace.log_analytics_workspace.id
  container_registry_id      = var.container_registry_id
  enable_private_endpoint    = false # Keeping simple for dev

  depends_on = [
    module.networking_app
  ]

  tags = local.common_tags
}
