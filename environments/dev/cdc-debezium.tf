# CDC Debezium Resource Group

locals {
  cdc_name = "oneview-debezium-cdc"
}

# ----------- Resource Group for CDC Debezium ----------- 

resource "azurerm_resource_group" "cdc_debezium" {
  name     = "${var.environment}-debezium-rg"
  location = var.location

  tags = {
    Accessibility       = "Internal"
    Application         = "CDC-Debezium"
    BusinessCriticality = "Medium"
    CostCenter          = "Engineering"
    CreatedBy           = "Terraform"
    CreationDate        = "2025-07-01"
    Environment         = var.environment
    ExpirationDate      = "Not-Set-Use-YYYY-MM-DD"
    Owner               = "<EMAIL>"
    Project             = "CDC-Integration"
    Usage               = "Development"
  }

  lifecycle {
    ignore_changes = [
      tags["CreatedBy"],
      tags["CreationDate"]
    ]
  }
}

# ----------- Event Hub resources for CDC Debezium ----------- 

resource "azurerm_eventhub_namespace" "cdc" {
  name                = "${var.environment}-debezium-ehns"
  location            = var.location
  resource_group_name = azurerm_resource_group.cdc_debezium.name
  sku                 = "Standard"
  capacity            = 1

  tags = {
    Accessibility       = "Internal,On-premise,External,Public"
    Application         = "Not-Set"
    BusinessCriticality = "Low/Medium"
    CostCenter          = "Not-Set"
    CreatedBy           = "Terraform"
    CreationDate        = "2025-07-01"
    Environment         = var.environment
    ExpirationDate      = "Not-Set-Use-YYYY-MM-DD"
    Owner               = "<EMAIL>"
    Project             = "Not-Set"
    Usage               = "Development"
  }
}

resource "azurerm_eventhub" "schema_history" {
  name                = "schema-history"
  namespace_name      = azurerm_eventhub_namespace.cdc.name
  resource_group_name = azurerm_eventhub_namespace.cdc.resource_group_name
  partition_count     = 1
  message_retention   = 7
}

resource "azurerm_eventhub" "changes" {
  name                = local.cdc_name
  namespace_name      = azurerm_eventhub_namespace.cdc.name
  resource_group_name = azurerm_eventhub_namespace.cdc.resource_group_name
  partition_count     = 2
  message_retention   = 7
}

resource "azurerm_eventhub" "changes_dlq" {
  name                = "${local.cdc_name}-dlq"
  namespace_name      = azurerm_eventhub_namespace.cdc.name
  resource_group_name = azurerm_eventhub_namespace.cdc.resource_group_name
  partition_count     = 1
  message_retention   = 7
}

resource "azurerm_eventhub_consumer_group" "changes_cg" {
  name                = "${local.cdc_name}-cg"
  namespace_name      = azurerm_eventhub_namespace.cdc.name
  eventhub_name       = azurerm_eventhub.changes.name
  resource_group_name = azurerm_eventhub_namespace.cdc.resource_group_name

  depends_on = [
    azurerm_eventhub.changes
  ]
}

resource "azurerm_eventhub_consumer_group" "changes_dlq_cg" {
  name                = "${local.cdc_name}-dlq-cg"
  namespace_name      = azurerm_eventhub_namespace.cdc.name
  eventhub_name       = azurerm_eventhub.changes_dlq.name
  resource_group_name = azurerm_eventhub_namespace.cdc.resource_group_name

  depends_on = [
    azurerm_eventhub.changes_dlq
  ]
}

resource "azurerm_eventhub_namespace_authorization_rule" "cdc_root" {
  name                = "TheRootManageSharedAccessKey"
  namespace_name      = azurerm_eventhub_namespace.cdc.name
  resource_group_name = azurerm_eventhub_namespace.cdc.resource_group_name

  listen = true
  send   = true
  manage = true
}

# ----------- Debezium Connector for CDC -----------

# Debezium Connect Container App
resource "azurerm_container_app" "debezium_connect" {
  name                         = "${var.environment}-debezium-ca"
  container_app_environment_id = module.container_environment.environment_id
  resource_group_name          = azurerm_resource_group.cdc_debezium.name
  revision_mode                = "Single"
  workload_profile_name        = "Consumption"

  ingress {
    allow_insecure_connections = false
    external_enabled           = true
    target_port                = 8083
    transport                  = "http"

    traffic_weight {
      percentage      = 100
      latest_revision = true
    }
  }

  template {
    container {
      name   = "debezium-connect"
      image  = "quay.io/debezium/connect:2.7"
      cpu    = "1.0"
      memory = "2Gi"

      env {
        name  = "BOOTSTRAP_SERVERS"
        value = "${azurerm_eventhub_namespace.cdc.name}.servicebus.windows.net:9093"
      }
      env {
        name  = "GROUP_ID"
        value = "1"
      }
      env {
        name  = "CONFIG_STORAGE_TOPIC"
        value = "debezium_configs"
      }
      env {
        name  = "OFFSET_STORAGE_TOPIC"
        value = "debezium_offsets"
      }
      env {
        name  = "STATUS_STORAGE_TOPIC"
        value = "debezium_statuses"
      }
      env {
        name  = "CONNECT_KEY_CONVERTER_SCHEMAS_ENABLE"
        value = "false"
      }
      env {
        name  = "CONNECT_VALUE_CONVERTER_SCHEMAS_ENABLE"
        value = "true"
      }
      env {
        name  = "CONNECT_REQUEST_TIMEOUT_MS"
        value = "60000"
      }
      env {
        name  = "CONNECT_SECURITY_PROTOCOL"
        value = "SASL_SSL"
      }
      env {
        name  = "CONNECT_SASL_MECHANISM"
        value = "PLAIN"
      }
      env {
        name  = "CONNECT_SASL_JAAS_CONFIG"
        value = "org.apache.kafka.common.security.plain.PlainLoginModule required username=\"$$ConnectionString\" password=\"${azurerm_eventhub_namespace_authorization_rule.cdc_root.primary_connection_string}\";"
      }
      env {
        name  = "CONNECT_PRODUCER_SECURITY_PROTOCOL"
        value = "SASL_SSL"
      }
      env {
        name  = "CONNECT_PRODUCER_SASL_MECHANISM"
        value = "PLAIN"
      }
      env {
        name  = "CONNECT_PRODUCER_SASL_JAAS_CONFIG"
        value = "org.apache.kafka.common.security.plain.PlainLoginModule required username=\"$$ConnectionString\" password=\"${azurerm_eventhub_namespace_authorization_rule.cdc_root.primary_connection_string}\";"
      }
      env {
        name  = "CONNECT_CONSUMER_SECURITY_PROTOCOL"
        value = "SASL_SSL"
      }
      env {
        name  = "CONNECT_CONSUMER_SASL_MECHANISM"
        value = "PLAIN"
      }
      env {
        name  = "CONNECT_CONSUMER_SASL_JAAS_CONFIG"
        value = "org.apache.kafka.common.security.plain.PlainLoginModule required username=\"$$ConnectionString\" password=\"${azurerm_eventhub_namespace_authorization_rule.cdc_root.primary_connection_string}\";"
      }
      env {
        name  = "SQL_SERVER_CONNECTION_STRING"
        value = "jdbc:sqlserver://${azurerm_mssql_server.sql_server.name}.database.windows.net:1433;database=master;user=${azurerm_mssql_server.sql_server.administrator_login};password=${random_password.sql_admin_password.result};encrypt=true;trustServerCertificate=false;hostNameInCertificate=*.database.windows.net;loginTimeout=30;"
      }
    }

    init_container {
      name   = "debezium-connector-setup"
      image  = "curlimages/curl:latest"
      cpu    = "0.25"
      memory = "0.5Gi"

      command = ["/bin/sh"]
      args = [
        "-c",
        <<-EOT
        echo "Waiting for Debezium Connect to be ready...";
        until curl -f http://localhost:8083/connectors; do
          echo "Debezium Connect not ready, waiting 30 seconds...";
          sleep 30;
        done;
        echo "Debezium Connect is ready, creating connector...";
        
        # Create the connector setup script
        cat > /tmp/create_connector.sh << 'SCRIPT_EOF'
curl -X POST \
  -H "Content-Type: application/json" \
  -d '{
    "name": "${local.cdc_name}",
    "config": {
      "snapshot.mode": "no_data",
      "connector.class": "io.debezium.connector.sqlserver.SqlServerConnector",
      "database.hostname": "${azurerm_mssql_server.sql_server.name}.database.windows.net",
      "database.port": "1433",
      "database.user": "${azurerm_mssql_server.sql_server.administrator_login}",
      "database.password": "${random_password.sql_admin_password.result}",
      "database.names": "OneviewDev",
      "driver.encrypt": "false",
      "driver.trustServerCertificate": "true",
      "schema.history.internal.kafka.bootstrap.servers": "${azurerm_eventhub_namespace.cdc.name}.servicebus.windows.net:9093",
      "schema.history.internal.kafka.topic": "schema-history",
      "schema.history.internal.consumer.security.protocol": "SASL_SSL",
      "schema.history.internal.consumer.sasl.mechanism": "PLAIN",
      "schema.history.internal.consumer.sasl.jaas.config": "org.apache.kafka.common.security.plain.PlainLoginModule required username=\"$$ConnectionString\" password=\"${azurerm_eventhub_namespace_authorization_rule.cdc_root.primary_connection_string}\";",
      "schema.history.internal.producer.security.protocol": "SASL_SSL",
      "schema.history.internal.producer.sasl.mechanism": "PLAIN",
      "schema.history.internal.producer.sasl.jaas.config": "org.apache.kafka.common.security.plain.PlainLoginModule required username=\"$$ConnectionString\" password=\"${azurerm_eventhub_namespace_authorization_rule.cdc_root.primary_connection_string}\";",
      "table.include.list": "dbo.Stock",
      "tombstones.on.delete": "false",
      "topic.prefix": "oneview-dev",
      "transforms": "Reroute",
      "transforms.Reroute.type": "io.debezium.transforms.ByLogicalTableRouter",
      "transforms.Reroute.topic.regex": "oneview-dev\\\\.(.*)",
      "transforms.Reroute.topic.replacement": "${local.cdc_name}"
    }
  }' \
  http://localhost:8083/connectors/
SCRIPT_EOF

        # Make the script executable
        chmod +x /tmp/create_connector.sh
        
        # Echo the script content for debugging
        echo "=== Connector creation script content ==="
        cat /tmp/create_connector.sh
        echo "=== End of script content ==="
        
        # Execute the script and capture output
        echo "=== Executing connector creation script ==="
        /tmp/create_connector.sh
        echo "=== Script execution completed ==="
        
        # Exit after successful completion
        exit 0
        EOT
      ]
    }

    min_replicas = 1
    max_replicas = 3
  }

  tags = {
    Accessibility       = "Internal"
    Application         = "CDC-Debezium"
    BusinessCriticality = "Medium"
    CostCenter          = "Engineering"
    CreatedBy           = "Terraform"
    CreationDate        = "2025-07-01"
    Environment         = var.environment
    ExpirationDate      = "Not-Set-Use-YYYY-MM-DD"
    Owner               = "<EMAIL>"
    Project             = "CDC-Integration"
    Usage               = "Development"
  }

  depends_on = [
    module.container_environment,
    azurerm_eventhub_namespace.cdc,
    azurerm_eventhub.schema_history,
    azurerm_eventhub.changes,
    azurerm_eventhub_namespace_authorization_rule.cdc_root
  ]
}
