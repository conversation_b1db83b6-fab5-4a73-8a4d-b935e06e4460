trigger:
  branches:
    include:
      - main

variables:
  terraformVersion: '1.9.8'
  workingDirectory: '$(System.DefaultWorkingDirectory)/environments/dev'
  ACCESS_KEY: ''
  CLOUDFLARE_ZONE_ID: 'b672e91867e9531256869fb2c877ab9e'

pool:
  vmImage: 'ubuntu-22.04'


parameters:
- name: refreshState
  type: boolean
  default: false

# displayName cannot be the first property in a step https://learn.microsoft.com/en-us/azure/devops/pipelines/yaml-schema/steps-script?view=azure-pipelines
# If a step is a script step, `script` is required as first property.

stages:
- stage: Plan
  displayName: 'Terraform Plan Dev'
  jobs:
  - job: TerraformPlan
    displayName: 'Terraform Plan'
    timeoutInMinutes: 1440 # 24 hours
    steps:
    - script: |
        wget https://releases.hashicorp.com/terraform/$(terraformVersion)/terraform_$(terraformVersion)_linux_amd64.zip
        unzip terraform_$(terraformVersion)_linux_amd64.zip
        sudo mv terraform /usr/local/bin/
        rm terraform_$(terraformVersion)_linux_amd64.zip
        terraform version
      displayName: 'Install Terraform'

    # Variable syntax https://learn.microsoft.com/en-us/azure/devops/pipelines/process/variables?view=azure-devops&tabs=yaml,batch#understand-variable-syntax
    - script: |
        az login --service-principal -u $(ARM_CLIENT_ID) -p $(ARM_CLIENT_SECRET) --tenant $(ARM_TENANT_ID)
        az account set --subscription $(ARM_SUBSCRIPTION_ID)
        ACCOUNT_KEY=$(az storage account keys list --resource-group infra-management --account-name gbagrouptfstate --query '[0].value' -o tsv)
        echo "##vso[task.setvariable variable=ACCESS_KEY;isOutput=true]$ACCOUNT_KEY"
      displayName: 'Get storage account key'
      name: GetStorageAccountKey

    - script: |
        cd $(workingDirectory)
        terraform init
      displayName: 'Terraform Init'
      env:
        ARM_ACCESS_KEY: $(GetStorageAccountKey.ACCESS_KEY)

    - script: |
        cd $(workingDirectory)
        terraform fmt -check -recursive
      displayName: 'Terraform Format Check'

    - script: |
        cd $(workingDirectory)
        terraform validate
      displayName: 'Terraform Validate'

    - script: |
        cd $(workingDirectory)
        terraform apply -refresh-only
      displayName: 'Terraform Refresh'
      env:
        ARM_CLIENT_ID: $(ARM_CLIENT_ID)
        ARM_CLIENT_SECRET: $(ARM_CLIENT_SECRET)
        ARM_SUBSCRIPTION_ID: $(ARM_SUBSCRIPTION_ID)
        ARM_TENANT_ID: $(ARM_TENANT_ID)
        ARM_ACCESS_KEY: $(GetStorageAccountKey.ACCESS_KEY)
        CLOUDFLARE_API_TOKEN: $(CLOUDFLARE_API_TOKEN)
        TF_VAR_cloudflare_zone_id: $(CLOUDFLARE_ZONE_ID)
      condition: ${{ eq(parameters.refreshState, true) }}

    - script: |
        cd $(workingDirectory)
        terraform plan -out=plan
      displayName: 'Terraform Plan'
      env:
        ARM_CLIENT_ID: $(ARM_CLIENT_ID)
        ARM_CLIENT_SECRET: $(ARM_CLIENT_SECRET)
        ARM_SUBSCRIPTION_ID: $(ARM_SUBSCRIPTION_ID)
        ARM_TENANT_ID: $(ARM_TENANT_ID)
        ARM_ACCESS_KEY: $(GetStorageAccountKey.ACCESS_KEY)
        CLOUDFLARE_API_TOKEN: $(CLOUDFLARE_API_TOKEN)
        TF_VAR_cloudflare_zone_id: $(CLOUDFLARE_ZONE_ID)
        TF_VAR_container_registry_id: $(TF_VAR_container_registry_id)

  # Be aware that the approvers attribute is Email address and case-sensitive.
  - job: ManagementApproval
    displayName: 'Management Approval'
    pool: server
    dependsOn: TerraformPlan
    steps:
    - task: ManualValidation@1
      inputs:
        notifyUsers: '<EMAIL>' # string. Required. Notify users.
        approvers: '<EMAIL>' # string. Approvers.
        allowApproversToApproveTheirOwnRuns: true
        instructions: 'Please review the Terraform plan and approve if changes look correct'
        onTimeout: 'reject' # 'reject' | 'resume'. On timeout. Default: reject.

  - job: TerraformApply
    displayName: 'Terraform Apply'
    dependsOn: [ManagementApproval, TerraformPlan]
    variables:
      ACCESS_KEY: $[ dependencies.TerraformPlan.outputs['GetStorageAccountKey.ACCESS_KEY'] ]
    steps:
    - script: |
        wget https://releases.hashicorp.com/terraform/$(terraformVersion)/terraform_$(terraformVersion)_linux_amd64.zip
        unzip terraform_$(terraformVersion)_linux_amd64.zip
        sudo mv terraform /usr/local/bin/
        rm terraform_$(terraformVersion)_linux_amd64.zip
        terraform version
      displayName: 'Install Terraform'

    - script: |
        cd $(workingDirectory)
        terraform init
      displayName: 'Terraform Init'
      env:
        ARM_ACCESS_KEY: $(ACCESS_KEY)

    - script: |
        cd $(workingDirectory)
        terraform plan -out=plan
        terraform apply -auto-approve plan
      displayName: 'Terraform Apply'
      env:
        ARM_CLIENT_ID: $(ARM_CLIENT_ID)
        ARM_CLIENT_SECRET: $(ARM_CLIENT_SECRET)
        ARM_SUBSCRIPTION_ID: $(ARM_SUBSCRIPTION_ID)
        ARM_TENANT_ID: $(ARM_TENANT_ID)
        ARM_ACCESS_KEY: $(ACCESS_KEY)
        CLOUDFLARE_API_TOKEN: $(CLOUDFLARE_API_TOKEN)
        TF_VAR_cloudflare_zone_id: $(CLOUDFLARE_ZONE_ID)
        TF_VAR_container_registry_id: $(TF_VAR_container_registry_id)
